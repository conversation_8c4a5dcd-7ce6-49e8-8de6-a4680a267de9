package com.sun.englishlearning.screen.courses

import android.content.Context
import com.sun.englishlearning.data.model.Lesson
import com.sun.englishlearning.data.repository.LessonRepository
import com.sun.englishlearning.data.repository.LessonRepositoryImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class CoursesPresenter : CoursesContract.Presenter {

    private var view: CoursesContract.View? = null
    private var context: Context? = null
    private var isOngoingTabSelected = true
    private lateinit var lessonRepository: LessonRepository
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    fun setContext(context: Context) {
        this.context = context
        lessonRepository = LessonRepositoryImpl(context)
    }

    override fun loadOngoingLessons() {
        view?.showLoading()
        coroutineScope.launch {
            try {
                val lessonsResult = lessonRepository.getAllLessons()
                if (lessonsResult.isSuccess) {
                    val lessons = lessonsResult.getOrNull() ?: emptyList()
                    // Show first half as "ongoing" for demo
                    val ongoingLessons = lessons.take(lessons.size / 2)
                    view?.hideLoading()
                    view?.showOngoingLessons(ongoingLessons)
                } else {
                    view?.hideLoading()
                    view?.showError("Error loading lessons")
                }
            } catch (e: Exception) {
                view?.hideLoading()
                view?.showError(e.message ?: "Error loading ongoing lessons")
            }
        }
    }

    override fun loadCompletedLessons() {
        view?.showLoading()
        coroutineScope.launch {
            try {
                val lessonsResult = lessonRepository.getAllLessons()
                if (lessonsResult.isSuccess) {
                    val lessons = lessonsResult.getOrNull() ?: emptyList()
                    // Show second half as "completed" for demo
                    val completedLessons = lessons.drop(lessons.size / 2)
                    view?.hideLoading()
                    view?.showCompletedLessons(completedLessons)
                } else {
                    view?.hideLoading()
                    view?.showError("Error loading lessons")
                }
            } catch (e: Exception) {
                view?.hideLoading()
                view?.showError(e.message ?: "Error loading completed lessons")
            }
        }
    }

    override fun onTabSelected(isOngoing: Boolean) {
        isOngoingTabSelected = isOngoing
        view?.updateTabSelection(isOngoing)

        if (isOngoing) {
            loadOngoingLessons()
        } else {
            loadCompletedLessons()
        }
    }

    override fun onLessonClicked(lesson: Lesson) {
        view?.navigateToLessonDetail(lesson)
    }

    override fun refreshLessons() {
        // Simply reload the current tab since we don't have a cache to refresh
        if (isOngoingTabSelected) {
            loadOngoingLessons()
        } else {
            loadCompletedLessons()
        }
    }

    override fun attachView(view: CoursesContract.View?) {
        this.view = view
    }

    override fun detachView() {
        this.view = null
    }

    override fun onStart() {
        // Load initial data
        onTabSelected(true) // Load ongoing lessons by default
    }

    override fun onStop() {

    }
    

    
    /**
     * Load all lessons from JSON file in assets
     * This displays all lessons from lessons.json
     */
    fun loadAllLessons() {
        view?.showLoading()
        coroutineScope.launch {
            try {
                val lessonsResult = lessonRepository.getAllLessons()
                if (lessonsResult.isSuccess) {
                    val lessons = lessonsResult.getOrNull() ?: emptyList()
                    view?.hideLoading()
                    view?.showOngoingLessons(lessons) // Display all lessons
                } else {
                    view?.hideLoading()
                    view?.showError("Error loading lessons from JSON")
                }
            } catch (e: Exception) {
                view?.hideLoading()
                view?.showError(e.message ?: "Error loading lessons from JSON")
            }
        }
    }
}

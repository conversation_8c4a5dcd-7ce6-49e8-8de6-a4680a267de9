package com.sun.englishlearning.screen.courses

import android.content.Context
import com.google.firebase.auth.FirebaseAuth
import com.sun.englishlearning.data.model.Lesson
import com.sun.englishlearning.api.LessonApiService
import com.sun.englishlearning.utils.JsonUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class CoursesPresenter : CoursesContract.Presenter {

    private var view: CoursesContract.View? = null
    private var context: Context? = null
    private var isOngoingTabSelected = true
    private val lessonApiService = LessonApiService()
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    fun setContext(context: Context) {
        this.context = context
    }

    override fun loadOngoingLessons() {
        view?.showLoading()
        coroutineScope.launch {
            try {
                val lessons = withContext(Dispatchers.IO) {
                    context?.let { JsonUtils.loadLessonsFromAssets(it) } ?: emptyList()
                }

                // Filter ongoing lessons (for demo, show first half of lessons)
                val ongoingLessons = lessons.take(lessons.size / 2)
                view?.hideLoading()
                view?.showOngoingLessons(ongoingLessons)
            } catch (e: Exception) {
                view?.hideLoading()
                view?.showError(e.message ?: "Error loading ongoing lessons")
            }
        }
    }

    override fun loadCompletedLessons() {
        view?.showLoading()
        coroutineScope.launch {
            try {
                val lessons = withContext(Dispatchers.IO) {
                    context?.let { JsonUtils.loadLessonsFromAssets(it) } ?: emptyList()
                }

                // Filter completed lessons (for demo, show second half of lessons)
                val completedLessons = lessons.drop(lessons.size / 2)
                view?.hideLoading()
                view?.showCompletedLessons(completedLessons)
            } catch (e: Exception) {
                view?.hideLoading()
                view?.showError(e.message ?: "Error loading completed lessons")
            }
        }
    }

    override fun onTabSelected(isOngoing: Boolean) {
        isOngoingTabSelected = isOngoing
        view?.updateTabSelection(isOngoing)

        if (isOngoing) {
            loadOngoingLessons()
        } else {
            loadCompletedLessons()
        }
    }

    override fun onLessonClicked(lesson: Lesson) {
        view?.navigateToLessonDetail(lesson)
    }

    override fun refreshLessons() {
        // Simply reload the current tab since we don't have a cache to refresh
        if (isOngoingTabSelected) {
            loadOngoingLessons()
        } else {
            loadCompletedLessons()
        }
    }

    override fun attachView(view: CoursesContract.View?) {
        this.view = view
    }

    override fun detachView() {
        this.view = null
    }

    override fun onStart() {
        // Load initial data
        onTabSelected(true) // Load ongoing lessons by default
    }

    override fun onStop() {

    }
    
    private fun getCurrentUserId(): String {
        // Get the current user ID from Firebase Authentication
        return FirebaseAuth.getInstance().currentUser?.uid ?: ""
    }
    
    private fun isLessonCompleted(lessonId: String): Boolean {
        // This would typically check UserLessonProgress, but for now return false
        // You can implement this logic based on your UserLessonProgress data
        return false
    }
    
    /**
     * Load all lessons from JSON file in assets
     * This displays all lessons from lessons.json
     */
    fun loadAllLessons() {
        view?.showLoading()
        coroutineScope.launch {
            try {
                val lessons = withContext(Dispatchers.IO) {
                    context?.let { JsonUtils.loadLessonsFromAssets(it) } ?: emptyList()
                }
                view?.hideLoading()
                view?.showOngoingLessons(lessons) // Display all lessons
            } catch (e: Exception) {
                view?.hideLoading()
                view?.showError(e.message ?: "Error loading lessons from JSON")
            }
        }
    }
}

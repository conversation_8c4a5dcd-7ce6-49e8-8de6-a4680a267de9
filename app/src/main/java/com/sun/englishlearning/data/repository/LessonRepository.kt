package com.sun.englishlearning.data.repository

import android.content.Context
import com.sun.englishlearning.data.model.Lesson
import com.sun.englishlearning.utils.JsonUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository interface for managing lesson data
 */
interface LessonRepository {
    /**
     * Get all lessons from JSON file
     */
    suspend fun getAllLessons(): Result<List<Lesson>>
    
    /**
     * Get a specific lesson by ID
     */
    suspend fun getLesson(lessonId: String): Result<Lesson?>
    
    /**
     * Get lessons by category/topic
     */
    suspend fun getLessonsByCategory(category: String): Result<List<Lesson>>
}

/**
 * Implementation of LessonRepository that loads data from JSON assets
 */
class LessonRepositoryImpl(
    private val context: Context?
) : LessonRepository {

    override suspend fun getAllLessons(): Result<List<Lesson>> {
        return try {
            withContext(Dispatchers.IO) {
                val lessons = JsonUtils.loadLessonsFromAssets(context)
                Result.success(lessons)
            }
        } catch (e: Exception) {
            Result.failure(Exception("Failed to load lessons from JSON: ${e.message}", e))
        }
    }

    override suspend fun getLesson(lessonId: String): Result<Lesson?> {
        return try {
            withContext(Dispatchers.IO) {
                val lessons = JsonUtils.loadLessonsFromAssets(context)
                val lesson = lessons.find { it.id == lessonId }
                Result.success(lesson)
            }
        } catch (e: Exception) {
            Result.failure(Exception("Failed to find lesson with ID $lessonId: ${e.message}", e))
        }
    }

    override suspend fun getLessonsByCategory(category: String): Result<List<Lesson>> {
        return try {
            withContext(Dispatchers.IO) {
                val lessons = JsonUtils.loadLessonsFromAssets(context)
                // Filter lessons by category (you can implement category logic based on lesson name or description)
                val filteredLessons = lessons.filter { lesson ->
                    lesson.title.contains(category, ignoreCase = true) ||
                    lesson.description.contains(category, ignoreCase = true)
                }
                Result.success(filteredLessons)
            }
        } catch (e: Exception) {
            Result.failure(Exception("Failed to load lessons for category $category: ${e.message}", e))
        }
    }
}

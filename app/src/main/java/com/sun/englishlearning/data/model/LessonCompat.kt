package com.sun.englishlearning.data.model

import java.io.Serializable

/**
 * Legacy Lesson model for backward compatibility with existing Home screen
 * This allows us to keep the Home screen unchanged while using the new JSON-based model
 * for Courses and LessonDetail screens
 */
data class LessonCompat(
    val id: String = "",
    val title: String = "",
    val description: String = "",
    val imageRes: Int = 0,
    val imageUrl: String = "",
    val vocabulary: List<String> = emptyList()
) : Serializable

/**
 * Extension function to convert new Lesson model to legacy LessonCompat
 */
fun Lesson.toLegacyLesson(): LessonCompat {
    return LessonCompat(
        id = this.id,
        title = this.name,
        description = this.description,
        imageRes = 0, // No image resource in new model
        imageUrl = this.image,
        vocabulary = this.vocabulary
    )
}

/**
 * Extension function to convert legacy LessonCompat to new Lesson model
 */
fun LessonCompat.toNewLesson(): Lesson {
    return Lesson(
        id = this.id,
        name = this.title,
        description = this.description,
        image = this.imageUrl,
        vocabulary = this.vocabulary
    )
}
